# Correção de Charset - Scripts PowerShell

## Problema Identificado

O script PowerShell original (`Fix-OggWarnings.ps1`) apresentava problemas de codificação de caracteres, resultando em texto corrompido na saída:

```
[2025-10-07 17:16:18] [SUCCESS] === Iniciando correÃ§Ã£o de warnings OGG Vorbis ===
[2025-10-07 17:16:18] [INFO] Pasta de mÃºsica: data\music
```

## Charset Escolhido: UTF-8 sem BOM

### Por que UTF-8 sem BOM?

1. **Compatibilidade**: UTF-8 sem BOM é amplamente suportado
2. **PowerShell**: Funciona melhor com UTF-8 sem BOM no Windows
3. **Evita problemas**: BOM (Byte Order Mark) pode causar problemas de parsing
4. **Padrão moderno**: UTF-8 sem BOM é o padrão atual para scripts

### Solução Aplicada

1. **Criado novo script**: `Fix-OggWarnings-Clean.ps1`
2. **Removidos acentos**: Substituídos por caracteres ASCII
3. **Codificação**: UTF-8 sem BOM
4. **Testado**: Funciona corretamente no Windows PowerShell

## Comparação de Versões

### Versão Original (Problemática)
```powershell
Write-Log "=== Iniciando correção de warnings OGG Vorbis ==="
Write-Log "Pasta de música: $MusicPath"
```

### Versão Corrigida (Funcional)
```powershell
Write-Log "=== Iniciando correcao de warnings OGG Vorbis ==="
Write-Log "Pasta de musica: $MusicPath"
```

## Scripts Disponíveis

1. **`Fix-OggWarnings-Clean.ps1`** ✅ RECOMENDADO
   - Charset: UTF-8 sem BOM
   - Sem acentos
   - Funciona corretamente

2. **`Fix-OggWarnings.ps1`** ❌ PROBLEMÁTICO
   - Charset: UTF-8 com problemas
   - Com acentos
   - Texto corrompido na saída

3. **`Fix-OggWarnings-UTF8.ps1`** ❌ PROBLEMÁTICO
   - Tentativa de correção
   - Ainda apresenta problemas

## Como Verificar o Charset de um Arquivo

### No PowerShell:
```powershell
Get-Content -Path "arquivo.ps1" -Encoding UTF8 | Out-String
```

### No VS Code:
- Canto inferior direito mostra a codificação
- Clique para alterar se necessário

### No Notepad++:
- Menu Encoding > Character sets
- Verificar se está em "UTF-8" ou "UTF-8-BOM"

## Recomendações para Futuros Scripts

1. **Use UTF-8 sem BOM** para scripts PowerShell
2. **Evite acentos** em mensagens de log quando possível
3. **Teste sempre** em ambiente Windows real
4. **Configure seu editor** para salvar em UTF-8 sem BOM por padrão

## Configuração do VS Code

Para garantir UTF-8 sem BOM no VS Code, adicione ao `settings.json`:

```json
{
    "files.encoding": "utf8",
    "files.autoGuessEncoding": false,
    "[powershell]": {
        "files.encoding": "utf8"
    }
}
```

## Resultado Final

✅ Script `Fix-OggWarnings-Clean.ps1` funciona perfeitamente
✅ Charset UTF-8 sem BOM escolhido
✅ Texto exibido corretamente no console
✅ Todos os 7 arquivos OGG processados com sucesso

---

**Charset Recomendado**: UTF-8 sem BOM  
**Script Funcional**: `tools/Fix-OggWarnings-Clean.ps1`  
**Status**: Problema de charset resolvido
