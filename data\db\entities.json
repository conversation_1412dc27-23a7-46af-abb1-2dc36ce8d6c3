{
	"0": {
		"Name": "Player",
		"SpritePreset": "Player",
		"Behaviour": ["None"],
		"Collision": "Medium",
		"Radius": "10",
		"DisplayName": true,
		"Stat": {
			"walkSpeed": 160,
		},
	},
	"1": {
		"Name": "Ship",
		"SpritePreset": "Ship",
		"Behaviour": ["None"],
		"Collision": "Medium",
		"Radius": "10",
		"Stat": {
			"walkSpeed": 20,
			"attack": 0,
			"defense": 0,
		},
	},

	"1000": {
		"Name": "<PERSON>p<PERSON>yna",
		"SpritePreset": "<PERSON><PERSON><PERSON><PERSON>",
		"Behaviour": ["Aggressive", "Leader", "Spawner"],
		"Collision": "Large",
		"Radius": "32",
		"DisplayName": true,
		"Stat": {
			"walkSpeed": 50,
			"level": 10,
		},
		"Spawns": {
			"Lilah": 3,
			"<PERSON><PERSON>": 3,
		},
	},
	"1001": {
		"Name": "<PERSON>",
		"SpritePreset": "Slime",
		"Textures":
		{
			"Body": "sprites/monsters/dorian.png",
		},
		"Behaviour": ["Aggressive", "Leader", "<PERSON>eal"],
		"Collision": "Medium",
		"Radius": "10",
		"Stat": {
			"walkSpeed": 40,
			"level": 5,
		},
		"Drops": {
			"Dorian's Key": 1.0,
		},
	},
	"1002": {
		"Name": "Emil",
		"SpritePreset": "Slime",
		"Textures":
		{
			"Body": "sprites/monsters/emil.png",
		},
		"Behaviour": ["Neutral", "Follower", "Steal"],
		"Collision": "Medium",
		"Radius": "10",
		"Stat": {
			"walkSpeed": 40,
		},
	},
	"1003": {
		"Name": "Gabriel",
		"SpritePreset": "Slime",
		"Textures":
		{
			"Body": "sprites/monsters/gabriel.png",
		},
		"Behaviour": ["Aggressive", "Leader", "Steal"],
		"Collision": "Medium",
		"Radius": "10",
		"Stat": {
			"walkSpeed": 40,
			"level": 5,
		},
		"Drops": {
			"Gabriel's Key": 1.0,
		},
	},
	"1004": {
		"Name": "Lilah",
		"SpritePreset": "Slime",
		"Textures":
		{
			"Body": "sprites/monsters/lilah.png",
		},
		"Behaviour": ["Neutral", "Follower", "Steal"],
		"Collision": "Medium",
		"Radius": "10",
		"Stat": {
			"walkSpeed": 40,
		},
	},
	"1005": {
		"Name": "Lulea",
		"SpritePreset": "Slime",
		"Textures":
		{
			"Body": "sprites/monsters/lulea.png",
		},
		"Behaviour": ["Neutral", "Follower", "Steal"],
		"Collision": "Medium",
		"Radius": "10",
		"Stat": {
			"walkSpeed": 40,
		},
	},
	"1006": {
		"Name": "Marvin",
		"SpritePreset": "Slime",
		"Textures":
		{
			"Body": "sprites/monsters/marvin.png",
		},
		"Behaviour": ["Aggressive", "Leader", "Spawner", "Steal"],
		"Collision": "Medium",
		"Radius": "10",
		"Stat": {
			"walkSpeed": 40,
			"level": 5,
		},
		"Drops": {
			"Marvin's Key": 1.0,
		},
		"Spawns": {
			"Lilah": 2,
		},
	},
	"1007": {
		"Name": "Salt Slime",
		"SpritePreset": "Slime",
		"Textures":
		{
			"Body": "sprites/monsters/slime-salt.png",
		},
		"Behaviour": ["Neutral", "Steal"],
		"Collision": "Medium",
		"Radius": "10",
		"Stat": {
			"walkSpeed": 40,
		},
		"Drops": {
			"Apple": 0.7,
		},
	},
	"1008": {
		"Name": "Piou",
		"SpritePreset": "Piou",
		"Collision": "Medium",
		"Radius": "10",
		"Stat": {
			"walkSpeed": 30,
		},
	},
	"1009": {
		"Name": "Drain Slime",
		"SpritePreset": "Slime",
		"Textures":
		{
			"Body": "sprites/monsters/slime-drain.png",
		},
		"Behaviour": ["Neutral", "Steal"],
		"Collision": "Medium",
		"Radius": "10",
		"Stat": {
			"walkSpeed": 70,
		},
	},
	"1010": {
		"Name": "Fire Skull",
		"SpritePreset": "Skull",
		"Textures":
		{
			"Body": "sprites/monsters/skull-fire.png",
		},
		"Behaviour": ["Aggressive"],
		"Collision": "Medium",
		"Radius": "10",
		"Stat": {
			"walkSpeed": 50,
			"level": 10,
		},
		"SkillSet": {
			"Flar": 1.0,
		},
	},
	"1011": {
		"Name": "Sludge Slime",
		"SpritePreset": "SlimeSludge",
		"Textures":
		{
			"Body": "sprites/monsters/slime-sludge.png",
		},
		"Behaviour": ["Neutral", "Steal"],
		"Collision": "Medium",
		"Radius": "10",
		"Stat": {
			"walkSpeed": 50,
		},
	},
	"1012": {
		"Name": "Ratto",
		"SpritePreset": "Ratto",
		"Behaviour": ["Neutral", "Steal"],
		"Collision": "Medium",
		"Radius": "10",
		"Stat": {
			"walkSpeed": 90,
		},
	},
	"1013": {
		"Name": "Turtle",
		"SpritePreset": "Turtle",
		"Collision": "Medium",
		"Radius": "10",
		"Stat": {
			"walkSpeed": 30,
			"attack": 1,
			"defense": 10,
		},
	},
	"1014": {
		"Name": "Croc",
		"SpritePreset": "Croc",
		"Collision": "Medium",
		"Radius": "12",
		"Stat": {
			"walkSpeed": 80,
			"attack": 10,
			"defense": 10,
		},
	},
	"1015": {
		"Name": "Bat",
		"SpritePreset": "Bat",
		"Collision": "Medium",
		"Radius": "10",
		"Stat": {
			"walkSpeed": 100,
			"attack": 5,
			"defense": 1,
		},
	},
	"1016": {
		"Name": "Snake",
		"SpritePreset": "Generic4Directions",
		"Behaviour": ["Aggressive"],
		"Collision": "Medium",
		"Radius": "5",
		"Textures": {
			"Body": "sprites/monsters/snake.png",
		},
		"Stat": {
			"walkSpeed": 170,
			"castAttackDelay": 0.5,
		},
	},
	"1017": {
		"Name": "Fluffy",
		"SpritePreset": "Generic4Directions",
		"Collision": "Medium",
		"Radius": "10",
		"Textures": {
			"Body": "sprites/monsters/fluffy.png",
		},
		"Stat": {
			"walkSpeed": 50,
		},
	},
	"1018": {
		"Name": "Maggot",
		"SpritePreset": "Generic4Directions",
		"Collision": "Medium",
		"Radius": "10",
		"Textures": {
			"Body": "sprites/monsters/maggot.png",
		},
		"Stat": {
			"walkSpeed": 50,
		},
	},
	"1019": {
		"Name": "Fire Goblin",
		"SpritePreset": "Generic4Directions",
		"Collision": "Medium",
		"Radius": "10",
		"Textures": {
			"Body": "sprites/monsters/firegoblin.png",
		},
		"SkillSet": {
			"Spitfire": 2.0,
		},
		"Stat": {
			"walkSpeed": 80,
		},
	},
	"1020": {
		"Name": "Spiky Mushroom",
		"SpritePreset": "Generic4Directions",
		"Collision": "Medium",
		"Radius": "10",
		"Textures": {
			"Body": "sprites/monsters/mushroom-spiky.png",
		},
		"Stat": {
			"walkSpeed": 80,
		},
	},
	"1021": {
		"Name": "Evil Mushroom",
		"SpritePreset": "Generic4Directions",
		"Collision": "Medium",
		"Radius": "10",
		"Textures": {
			"Body": "sprites/monsters/mushroom-evil.png",
		},
		"Stat": {
			"walkSpeed": 80,
		},
	},
	"1022": {
		"Name": "Pinkie",
		"SpritePreset": "Generic4Directions",
		"Collision": "Medium",
		"Radius": "20",
		"Textures": {
			"Body": "sprites/monsters/pinkie.png",
		},
		"Stat": {
			"walkSpeed": 80,
		},
	},
	"1023": {
		"Name": "Giant Maggot",
		"SpritePreset": "Generic4Directions",
		"Collision": "Large",
		"Radius": "70",
		"Textures": {
			"Body": "sprites/monsters/maggot-giant.png",
		},
		"Stat": {
			"walkSpeed": 80,
		},
	},
	"1024": {
		"Name": "Scorpion",
		"SpritePreset": "Generic4Directions",
		"Collision": "Medium",
		"Radius": "30",
		"Textures": {
			"Body": "sprites/monsters/scorpion.png",
		},
		"Stat": {
			"walkSpeed": 60,
		},
	},
	"1025": {
		"Name": "Skeleton",
		"SpritePreset": "Player",
		"Collision": "Medium",
		"Radius": "16",
		"Textures": {
			"Body": "sprites/monsters/skeleton.png",
		},
		"Stat": {
			"walkSpeed": 80,
		},
	},
	"1025": {
		"Name": "Bird",
		"SpritePreset": "Bird",
		"Collision": "Medium",
		"Radius": "16",
		"Stat": {
			"walkSpeed": 200,
			"castAttackDelay": 0.1,
		},
	},
	"1026": {
		"Name": "Lizzy",
		"SpritePreset": "Lizzy",
		"Collision": "Medium",
		"Radius": "16",
		"Stat": {
			"walkSpeed": 80,
		},
	},
	"1027": {
		"Name": "Lizandra",
		"SpritePreset": "Lizzy",
		"Behaviour": ["Aggressive"],
		"Collision": "Medium",
		"Radius": "16",
		"Textures": {
			"Body": "sprites/monsters/lizandra.png",
		},
		"Stat": {
			"walkSpeed": 80,
		},
	},
	"1028": {
		"Name": "Sand Snake",
		"SpritePreset": "Generic4Directions",
		"Behaviour": ["Aggressive"],
		"Collision": "Medium",
		"Radius": "5",
		"Textures": {
			"Body": "sprites/monsters/snake.png",
		},
		"Shaders": {
			"Body": "Snake Sand",
		},
		"Stat": {
			"walkSpeed": 110,
			"castAttackDelay": 0.5,
		},
	},
	"1029": {
		"Name": "Lynx",
		"SpritePreset": "Lynx",
		"Behaviour": ["Aggressive"],
		"Collision": "Medium",
		"Radius": "10",
		"SkillSet": {
			"Sonic Wave": 10.0,
		},
		"Stat": {
			"walkSpeed": 80,
			"level": 10,
		},
	},

	"2000": {
		"Name": "Bicies",
		"SpritePreset": "Player",
		"Equipment": [
			"V-Neck Tee",
			"Trousers",
		],
		"Behaviour": ["Pacifist"],
		"Collision": "Small",
		"Radius": "16",
		"DisplayName": true,
		"Stat": {
			"walkSpeed": 70,
			"hairstyle": "Center Parting",
			"haircolor": "Dark Brown",
			"gender": "Male",
			"race": "Human",
			"skintone": "Dark",
		},
	},
	"2001": {
		"Name": "Old Chest",
		"SpritePreset": "Chest",
		"Behaviour": ["Immobile", "Pacifist"],
		"Collision": "Large",
		"Radius": "32",
		"DisplayName": true,
	},
	"2002": {
		"Name": "Elanore",
		"SpritePreset": "Player",
		"Equipment": [
			"V-Neck Tee",
			"Trousers"
		],
		"Behaviour": ["Pacifist"],
		"Collision": "Small",
		"Radius": "16",
		"DisplayName": true,
		"Stat": {
			"walkSpeed": 50,
			"hairstyle": "Bun",
			"haircolor": "Forest Green",
			"gender": "Female",
			"race": "Human",
			"skintone": "Tanned",
		},
	},
	"2003": {
		"Name": "Locks",
		"SpritePreset": "CaveLocks",
		"Behaviour": ["Immobile", "Pacifist"],
		"Collision": "Large",
		"Radius": "32",
		"DisplayName": true,
	},
	"2004": {
		"Name": "Gabz",
		"SpritePreset": "Static",
		"Behaviour": ["Immobile", "Pacifist"],
		"Collision": "Small",
		"Radius": "16",
		"DisplayName": true,
		"Textures":
		{
			"Body": "sprites/npcs/gabz.png",
		},
	},
}
