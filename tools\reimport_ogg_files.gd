@tool
extends EditorScript

# Script para forçar reimportação de arquivos OGG com configurações limpas
# Isso pode resolver problemas de comentários inválidos

func _run():
	print("=== Iniciando reimportação de arquivos OGG ===")
	
	var music_path = "res://data/music/"
	var files_to_reimport = []
	
	# Lista todos os arquivos .ogg na pasta de música
	var dir = DirAccess.open(music_path)
	if dir:
		dir.list_dir_begin()
		var file_name = dir.get_next()
		while file_name != "":
			if file_name.ends_with(".ogg"):
				files_to_reimport.append(music_path + file_name)
				print("Arquivo OGG encontrado: ", file_name)
			file_name = dir.get_next()
		dir.list_dir_end()
	
	print("Total de arquivos para reimportar: ", files_to_reimport.size())
	
	# Reimporta cada arquivo com configurações limpas
	for file_path in files_to_reimport:
		reimport_ogg_file(file_path)
	
	print("=== Reimportação concluída ===")
	print("Os arquivos foram reimportados com configurações padrão limpas")

func reimport_ogg_file(file_path: String):
	print("Reimportando: ", file_path)
	
	# Obtém o caminho do arquivo .import
	var import_path = file_path + ".import"
	
	# Cria configurações de importação limpas
	var import_config = ConfigFile.new()
	
	# Configurações básicas de remapeamento
	import_config.set_value("remap", "importer", "oggvorbisstr")
	import_config.set_value("remap", "type", "AudioStreamOggVorbis")
	
	# Gera um novo UID para forçar reimportação
	var new_uid = "uid://b" + str(randi()).md5_text().substr(0, 12)
	import_config.set_value("remap", "uid", new_uid)
	
	# Define o caminho de destino
	var dest_path = "res://.godot/imported/" + file_path.get_file() + "-" + str(randi()).md5_text().substr(0, 32) + ".oggvorbisstr"
	import_config.set_value("remap", "path", dest_path)
	
	# Configurações de dependências
	import_config.set_value("deps", "source_file", file_path)
	import_config.set_value("deps", "dest_files", [dest_path])
	
	# Parâmetros de importação limpos (sem loop por padrão)
	import_config.set_value("params", "loop", false)
	import_config.set_value("params", "loop_offset", 0)
	import_config.set_value("params", "bpm", 0)
	import_config.set_value("params", "beat_count", 0)
	import_config.set_value("params", "bar_beats", 4)
	
	# Salva o arquivo de importação
	var save_result = import_config.save(import_path)
	if save_result == OK:
		print("Arquivo .import atualizado: ", import_path)
		
		# Força a reimportação através do EditorInterface
		if Engine.is_editor_hint():
			EditorInterface.get_resource_filesystem().reimport_files([file_path])
			print("Reimportação solicitada para: ", file_path)
	else:
		print("ERRO ao salvar arquivo .import: ", import_path, " - Código: ", save_result)

func backup_import_files():
	print("Criando backup dos arquivos .import...")
	
	var music_path = "res://data/music/"
	var dir = DirAccess.open(music_path)
	if dir:
		dir.list_dir_begin()
		var file_name = dir.get_next()
		while file_name != "":
			if file_name.ends_with(".ogg.import"):
				var original_path = music_path + file_name
				var backup_path = original_path + ".backup"
				dir.copy(original_path, backup_path)
				print("Backup criado: ", backup_path)
			file_name = dir.get_next()
		dir.list_dir_end()
