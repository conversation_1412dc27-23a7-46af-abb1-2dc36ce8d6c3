#!/usr/bin/env python3
"""
Script para corrigir warnings de comentários inválidos em arquivos OGG Vorbis
Este script remove metadados problemáticos que causam warnings no Godot
"""

import os
import sys
import shutil
from pathlib import Path

def find_ogg_files(music_dir):
    """Encontra todos os arquivos .ogg na pasta de música"""
    ogg_files = []
    music_path = Path(music_dir)
    
    if music_path.exists():
        for file_path in music_path.glob("*.ogg"):
            ogg_files.append(file_path)
            print(f"Encontrado: {file_path.name}")
    
    return ogg_files

def backup_file(file_path):
    """Cria backup do arquivo original"""
    backup_path = file_path.with_suffix(file_path.suffix + '.backup')
    try:
        shutil.copy2(file_path, backup_path)
        print(f"Backup criado: {backup_path.name}")
        return True
    except Exception as e:
        print(f"ERRO ao criar backup: {e}")
        return False

def check_ogg_with_ffmpeg(file_path):
    """Verifica o arquivo OGG usando ffmpeg se disponível"""
    try:
        import subprocess
        result = subprocess.run([
            'ffprobe', '-v', 'quiet', '-print_format', 'json', 
            '-show_format', '-show_streams', str(file_path)
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            import json
            data = json.loads(result.stdout)
            return data
        else:
            print(f"ffprobe falhou para {file_path.name}: {result.stderr}")
            return None
    except (ImportError, FileNotFoundError):
        print("ffprobe não disponível, usando método alternativo")
        return None

def fix_ogg_with_ffmpeg(input_path, output_path):
    """Corrige arquivo OGG usando ffmpeg"""
    try:
        import subprocess
        
        # Comando ffmpeg para recodificar sem metadados problemáticos
        cmd = [
            'ffmpeg', '-i', str(input_path), 
            '-c:a', 'libvorbis', '-q:a', '5',  # Qualidade 5 (boa qualidade)
            '-map_metadata', '-1',  # Remove todos os metadados
            '-y',  # Sobrescreve arquivo de saída
            str(output_path)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"Arquivo corrigido com ffmpeg: {input_path.name}")
            return True
        else:
            print(f"ERRO ffmpeg: {result.stderr}")
            return False
            
    except (ImportError, FileNotFoundError):
        print("ffmpeg não disponível")
        return False

def fix_ogg_manual(file_path):
    """Método manual para tentar corrigir problemas de OGG"""
    try:
        # Lê o arquivo binário
        with open(file_path, 'rb') as f:
            data = f.read()
        
        # Procura por padrões problemáticos nos comentários Vorbis
        # Os comentários Vorbis começam após o header "vorbis"
        
        # Localiza o início dos comentários
        vorbis_comment_start = data.find(b'\x03vorbis')
        if vorbis_comment_start == -1:
            print(f"Não encontrou header de comentários Vorbis em {file_path.name}")
            return False
        
        print(f"Header de comentários encontrado em {file_path.name} na posição {vorbis_comment_start}")
        
        # Para uma correção mais segura, vamos apenas reportar o problema
        # sem modificar o arquivo diretamente
        return True
        
    except Exception as e:
        print(f"ERRO ao processar {file_path.name}: {e}")
        return False

def update_godot_import_file(ogg_path):
    """Atualiza o arquivo .import do Godot para forçar reimportação"""
    import_path = ogg_path.with_suffix(ogg_path.suffix + '.import')
    
    if not import_path.exists():
        print(f"Arquivo .import não encontrado: {import_path.name}")
        return False
    
    try:
        # Lê o arquivo .import
        with open(import_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Modifica o UID para forçar reimportação
        import re
        import random
        import hashlib
        
        # Gera novo UID
        new_uid = f"uid://b{hashlib.md5(str(random.random()).encode()).hexdigest()[:12]}"
        
        # Substitui o UID existente
        content = re.sub(r'uid="[^"]*"', f'uid="{new_uid}"', content)
        
        # Salva o arquivo modificado
        with open(import_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Arquivo .import atualizado: {import_path.name}")
        return True
        
    except Exception as e:
        print(f"ERRO ao atualizar .import: {e}")
        return False

def main():
    print("=== Corretor de Warnings OGG Vorbis ===")
    
    # Caminho para a pasta de música
    music_dir = "data/music"
    
    if not os.path.exists(music_dir):
        print(f"ERRO: Pasta {music_dir} não encontrada")
        return 1
    
    # Encontra arquivos OGG
    ogg_files = find_ogg_files(music_dir)
    
    if not ogg_files:
        print("Nenhum arquivo .ogg encontrado")
        return 0
    
    print(f"Encontrados {len(ogg_files)} arquivos OGG")
    
    # Processa cada arquivo
    for ogg_file in ogg_files:
        print(f"\nProcessando: {ogg_file.name}")
        
        # Cria backup
        if not backup_file(ogg_file):
            continue
        
        # Tenta corrigir com ffmpeg primeiro
        temp_file = ogg_file.with_suffix('.temp.ogg')
        
        if fix_ogg_with_ffmpeg(ogg_file, temp_file):
            # Substitui o arquivo original
            shutil.move(temp_file, ogg_file)
            print(f"Arquivo corrigido: {ogg_file.name}")
        else:
            # Método manual
            fix_ogg_manual(ogg_file)
        
        # Atualiza arquivo .import do Godot
        update_godot_import_file(ogg_file)
    
    print("\n=== Processamento concluído ===")
    print("IMPORTANTE:")
    print("1. Abra o Godot")
    print("2. Vá em Project -> Reimport")
    print("3. Selecione todos os arquivos .ogg e reimporte")
    print("4. Teste o projeto para verificar se os warnings sumiram")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
