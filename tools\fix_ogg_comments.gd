@tool
extends EditorScript

# Script para corrigir comentários inválidos em arquivos OGG Vorbis
# Este script reprocessa os arquivos OGG para remover comentários problemáticos

func _run():
	print("=== Iniciando correção de arquivos OGG Vorbis ===")
	
	var music_path = "res://data/music/"
	var files_to_fix = []
	
	# Lista todos os arquivos .ogg na pasta de música
	var dir = DirAccess.open(music_path)
	if dir:
		dir.list_dir_begin()
		var file_name = dir.get_next()
		while file_name != "":
			if file_name.ends_with(".ogg"):
				files_to_fix.append(music_path + file_name)
				print("Encontrado arquivo OGG: ", file_name)
			file_name = dir.get_next()
		dir.list_dir_end()
	
	print("Total de arquivos OGG encontrados: ", files_to_fix.size())
	
	# Processa cada arquivo
	for file_path in files_to_fix:
		fix_ogg_file(file_path)
	
	print("=== Correção concluída ===")
	print("IMPORTANTE: Reimporte os arquivos no Godot para aplicar as correções")

func fix_ogg_file(file_path: String):
	print("Processando: ", file_path)
	
	# Carrega o arquivo como AudioStreamOggVorbis
	var original_stream = load(file_path) as AudioStreamOggVorbis
	if not original_stream:
		print("ERRO: Não foi possível carregar ", file_path)
		return
	
	# Obtém os dados do arquivo
	var file_data = original_stream.data
	if not file_data or file_data.size() == 0:
		print("ERRO: Dados do arquivo estão vazios para ", file_path)
		return
	
	# Cria um novo stream limpo
	var new_stream = AudioStreamOggVorbis.new()
	new_stream.data = file_data
	
	# Remove configurações de loop que podem estar causando problemas
	new_stream.loop = false
	new_stream.loop_offset = 0.0
	
	# Cria um backup do arquivo original
	var backup_path = file_path + ".backup"
	var original_file = FileAccess.open(file_path, FileAccess.READ)
	var backup_file = FileAccess.open(backup_path, FileAccess.WRITE)
	
	if original_file and backup_file:
		backup_file.store_buffer(original_file.get_buffer(original_file.get_length()))
		backup_file.close()
		original_file.close()
		print("Backup criado: ", backup_path)
	else:
		print("AVISO: Não foi possível criar backup para ", file_path)
	
	# Salva o arquivo corrigido
	var result = ResourceSaver.save(new_stream, file_path)
	if result == OK:
		print("Arquivo corrigido com sucesso: ", file_path)
	else:
		print("ERRO ao salvar arquivo corrigido: ", file_path, " - Código: ", result)

func create_clean_ogg_from_data(original_data: PackedByteArray) -> AudioStreamOggVorbis:
	# Cria um novo stream OGG limpo a partir dos dados originais
	var new_stream = AudioStreamOggVorbis.new()
	new_stream.data = original_data
	new_stream.loop = false
	new_stream.loop_offset = 0.0
	return new_stream
