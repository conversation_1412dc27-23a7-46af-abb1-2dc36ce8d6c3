# Correção de Warnings OGG Vorbis - Source of Mana

## Problema Identificado

O projeto estava apresentando warnings relacionados a comentários inválidos em arquivos OGG Vorbis:

```
WARNING: Invalid comment in Ogg Vorbis file.
   at: maybe_update_info (modules/vorbis/audio_stream_ogg_vorbis.cpp:465)
```

## Solução Aplicada

Foi executado um script de correção que:

1. **Criou backups** de todos os arquivos .ogg originais (com extensão .backup)
2. **Atualizou os arquivos .import** do Godot com novos UIDs para forçar reimportação
3. **Modificou os caminhos de destino** para garantir que os arquivos sejam reprocessados

## Arquivos Processados

Os seguintes arquivos foram processados com sucesso:

- `<PERSON> - In the forest of the birches.ogg`
- `<PERSON> - The Adventure Begins.ogg`
- `Dariunas - Faith.ogg`
- `Hitctrl - Foreboding ambiance.ogg`
- `Jose. S & Dakota. D - Lenia.ogg`
- `Jose. S & Dakota. D - Night is calling.ogg`
- `NYC Sound Design & Dariunas - Cave.ogg`

## Próximos Passos (IMPORTANTE)

Para completar a correção, você deve:

### 1. Abrir o Projeto no Godot
- Abra o Godot Engine
- Carregue o projeto Source of Mana

### 2. Forçar Reimportação
- No FileSystem dock do Godot, navegue até `data/music/`
- Selecione todos os arquivos `.ogg`
- Clique com o botão direito e escolha "Reimport"
- OU vá em `Project > Reimport` no menu principal

### 3. Verificar a Correção
- Execute o projeto
- Observe o console/output para verificar se os warnings sumiram
- Os warnings de "Invalid comment in Ogg Vorbis file" não devem mais aparecer

## Scripts Criados

Foram criados os seguintes scripts para correção:

1. **`tools/Fix-OggWarnings.ps1`** - Script PowerShell principal (executado)
2. **`tools/fix_ogg_comments.gd`** - Script GDScript alternativo
3. **`tools/reimport_ogg_files.gd`** - Script GDScript para reimportação
4. **`tools/fix_ogg_warnings.py`** - Script Python alternativo

## Backups

Todos os arquivos originais foram preservados com a extensão `.backup`. Se algo der errado, você pode:

1. Deletar os arquivos .ogg atuais
2. Renomear os arquivos .backup removendo a extensão .backup
3. Executar novamente o processo de correção

## Verificação de Sucesso

Após seguir os passos acima, execute o projeto e verifique se:

- ✅ Não há mais warnings sobre "Invalid comment in Ogg Vorbis file"
- ✅ A música do jogo funciona normalmente
- ✅ Não há erros de carregamento de áudio

## Troubleshooting

Se os warnings persistirem:

1. **Verifique se a reimportação foi feita** - Os arquivos devem ser reprocessados pelo Godot
2. **Limpe o cache** - Delete a pasta `.godot/imported/` e reimporte novamente
3. **Use FFmpeg** - Se disponível, execute o script Python que pode recodificar os arquivos
4. **Contate o desenvolvedor** - Se o problema persistir, pode ser necessária análise adicional

## Ferramentas Utilizadas

- **PowerShell** - Para automação da correção
- **Godot Engine** - Sistema de importação nativo
- **Scripts GDScript** - Para processamento interno no Godot

---

**Data da Correção:** 2025-10-07  
**Status:** Correção aplicada - Aguardando reimportação no Godot
