@tool
extends EditorScript

# Script para testar o carregamento dos arquivos OGG corrigidos
# Execute este script no Godot para verificar se os warnings foram eliminados

func _run():
	print("=== Testando carregamento de arquivos OGG corrigidos ===")
	
	var music_path = "res://data/music/"
	var test_results = []
	var error_count = 0
	var success_count = 0
	
	# Lista todos os arquivos .ogg
	var dir = DirAccess.open(music_path)
	if not dir:
		print("ERRO: Não foi possível abrir a pasta ", music_path)
		return
	
	dir.list_dir_begin()
	var file_name = dir.get_next()
	var ogg_files = []
	
	while file_name != "":
		if file_name.ends_with(".ogg") and not file_name.ends_with(".backup"):
			ogg_files.append(music_path + file_name)
		file_name = dir.get_next()
	dir.list_dir_end()
	
	print("Encontrados ", ogg_files.size(), " arquivos OGG para testar")
	print("")
	
	# Testa cada arquivo
	for file_path in ogg_files:
		var result = test_ogg_file(file_path)
		test_results.append(result)
		
		if result.success:
			success_count += 1
			print("✅ ", result.filename, " - Carregado com sucesso")
		else:
			error_count += 1
			print("❌ ", result.filename, " - ERRO: ", result.error)
	
	print("")
	print("=== Resultados do Teste ===")
	print("Arquivos testados: ", ogg_files.size())
	print("Sucessos: ", success_count)
	print("Erros: ", error_count)
	
	if error_count == 0:
		print("🎉 SUCESSO! Todos os arquivos OGG foram carregados sem problemas!")
		print("Os warnings de comentários inválidos devem ter sido resolvidos.")
	else:
		print("⚠️  Alguns arquivos ainda apresentam problemas.")
		print("Verifique se a reimportação foi feita corretamente no Godot.")
	
	print("")
	print("=== Instruções Adicionais ===")
	if error_count > 0:
		print("1. Vá em Project > Reimport")
		print("2. Selecione todos os arquivos .ogg em data/music/")
		print("3. Clique em 'Reimport'")
		print("4. Execute este script novamente")
	else:
		print("1. Execute o projeto principal")
		print("2. Verifique se não há mais warnings no console")
		print("3. Teste a funcionalidade de música no jogo")

func test_ogg_file(file_path: String) -> Dictionary:
	var result = {
		"filename": file_path.get_file(),
		"success": false,
		"error": "",
		"stream": null
	}
	
	# Tenta carregar o arquivo como AudioStreamOggVorbis
	var stream = load(file_path)
	
	if not stream:
		result.error = "Falha ao carregar o arquivo"
		return result
	
	if not stream is AudioStreamOggVorbis:
		result.error = "Arquivo não é um AudioStreamOggVorbis válido"
		return result
	
	# Verifica se os dados estão presentes
	if not stream.data or stream.data.size() == 0:
		result.error = "Dados do arquivo estão vazios"
		return result
	
	# Tenta criar um AudioStreamPlayer para testar o stream
	var player = AudioStreamPlayer.new()
	player.stream = stream
	
	# Se chegou até aqui, o arquivo está OK
	result.success = true
	result.stream = stream
	
	# Limpa o player
	player.queue_free()
	
	return result

func check_import_files():
	print("=== Verificando arquivos .import ===")
	
	var music_path = "res://data/music/"
	var dir = DirAccess.open(music_path)
	if not dir:
		return
	
	dir.list_dir_begin()
	var file_name = dir.get_next()
	
	while file_name != "":
		if file_name.ends_with(".ogg.import"):
			var import_path = music_path + file_name
			var config = ConfigFile.new()
			var err = config.load(import_path)
			
			if err == OK:
				var uid = config.get_value("remap", "uid", "")
				var path = config.get_value("remap", "path", "")
				print("📄 ", file_name, " - UID: ", uid)
			else:
				print("❌ Erro ao ler ", file_name)
		
		file_name = dir.get_next()
	dir.list_dir_end()

func force_reimport_all():
	print("=== Forçando reimportação de todos os arquivos OGG ===")
	
	var music_path = "res://data/music/"
	var files_to_reimport = []
	
	var dir = DirAccess.open(music_path)
	if not dir:
		print("ERRO: Não foi possível abrir pasta de música")
		return
	
	dir.list_dir_begin()
	var file_name = dir.get_next()
	
	while file_name != "":
		if file_name.ends_with(".ogg") and not file_name.ends_with(".backup"):
			files_to_reimport.append(music_path + file_name)
		file_name = dir.get_next()
	dir.list_dir_end()
	
	if files_to_reimport.size() > 0:
		print("Reimportando ", files_to_reimport.size(), " arquivos...")
		EditorInterface.get_resource_filesystem().reimport_files(files_to_reimport)
		print("Reimportação solicitada. Aguarde o processamento...")
	else:
		print("Nenhum arquivo OGG encontrado para reimportar")
