name: "godot-ci export"
on: push

env:
  GODOT_VERSION: 4.4
  EXPORT_NAME: "Source of Mana"
  PROJECT_PATH: "."

jobs:
  builds:
    strategy:
      fail-fast: false
      matrix:
        include:
        - name: Linux
          export_template: "Linux/X11"
          ext: "x86_64"
        - name: Linux (Headless Server)
          export_template: "Linux/X11 Headless Server"
          ext: "x86_64"
        - name: Windows
          export_template: "Windows Desktop"
          ext: "exe"
        - name: macOS
          export_template: "macOS"
          ext: "zip"
        - name: Android
          export_template: "Android"
          ext: "apk"
    runs-on: ubuntu-latest
    permissions: write-all
    container:
      image: barichello/godot-ci:4.4
    name: Export ${{ matrix.name }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          lfs: true
      - name: Setup
        run: |
          mkdir -v -p ~/.local/share/godot/export_templates/
          mkdir -v -p ~/.config/
          cp -r /root/.config/godot ~/.config/godot
          cp -r /root/.local/share/godot/export_templates/${GODOT_VERSION}.stable ~/.local/share/godot/export_templates/${GODOT_VERSION}.stable
          echo "$DEBUG_KEYSTORE" | base64 -d > /root/debug.keystore
        env:
          DEBUG_KEYSTORE: ${{ secrets.SECRET_RELEASE_KEYSTORE_BASE64 }}
      - name: ${{ matrix.name }} Build
        run: |
          mkdir -v -p "build/${{ matrix.name }}"
          cd "$PROJECT_PATH"
          godot --headless --verbose --export-debug "${{ matrix.export_template }}" "build/${{ matrix.name }}/${EXPORT_NAME}.${{ matrix.ext }}"
      - name: Upload Artifact - ${{ matrix.name }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.name }}
          path: build/${{ matrix.name }}
