{"categories": [{"category": "Welcome to Source of Mana!", "entries": [{"content": "\tSource of Mana is a 2D MMORPG using Godot to provide a modern player experience for a classic style of game that never really goes out of fashion.\nIn Source of Mana you can live your medieval fantasy RPG adventure with friends and explore a world of magical tales and just pure silly fun.\n\nWe are still in the very early stages of development and updates will come soon.\nStay tuned and join us on Discord or IRC to discuss the project!\n"}, {"title": "Version 0.8", "date": "2025-04-22", "content": "Persistent World & Accounts:\n[ul]A fully functional account and character creation system.\nCustomize your character with over 15 new hairstyles and 20 hair colors.\nProgress is now saved thanks to a new SQL backend: quests, inventory, stats, and more.\nNew advanced server UI for better online world management.[/ul]\n\nExploration & Lore:\n[ul]Four new cave maps in the Tonori desert, including Sandstorm Mines and Snake Pit Caves.\n15+ new monsters introduced to populate these dangerous areas.\nEarly development of Tulimshar, a new city planned for the next milestone.[/ul]\n\nUser Interface:\n[ul]New login/account creation and character selection/customization screens.\nNew progress window combining a quest log and a bestiary.\nRedesigned inventory, minimap, and player stats interfaces.[/ul]\n\nCombat & Equipment:\n[ul]New zone-based projectiles and improved targeting logic.\nMonsters drop items on death.\nAdvanced inventory system: stacking, equipment slots, stat modifiers.\nOver 25 new equipment items added, with visible gear on your character sprite.[/ul]\n\nInfrastructure & Tech:\n[ul]WebSocket support for itch.io and browser play.\nSecure login system with password hashing and TLS.\nSwitched from GitLab to GitHub for centralized development.\nOngoing CI/CD automation within GitHub Actions.[/ul]\n\nThis release marks a major step toward a persistent and evolving world.\nThank you for playing! <PERSON>limshar and more await in the next milestone.\n"}, {"title": "Version 0.0.7", "date": "2024-10-26", "content": "Changelog:\n[ul]Sailing System: Explore the seas with the nw sailing mechanics and traverse new areas like Artis or drop anchor to explore the inside of your ship.\nImproved AI: Monsters now exhibit advanced behaviors such as stealing and spawning minions.\nQuest & Dialogue Support: The Splatyna Quest and Candor's Arena are introduced with a brand new NPC system.\nEquipment & Combat: Stackable equipment sprites and clearer HP management for entities, along with monster item drops.\nMap & Navigation: New maps like your ship LaJohanne, Artis the port city and various navigation system enhancements.\nUI & Feedback: Android, gameplay and keyboard advanced button bindings to work equaly on all supported platforms.\nNew Monsters: Introducing new monsters like <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, each with their own unique behaviors.\nSystem Enhancements: Resolved bugs such as respawning issues, CPU overload on Linux, and improved device detection.\nArt & Assets: New and improved tilesets, recolor palettes and so many more![/ul]\nWe’re excited to bring you these updates and appreciate your continued support and feedback!\n"}, {"title": "Version 0.0.6", "date": "2024-05-18", "content": "Game:\n[ul]\nFont visibility improvements\nEnemy/Ally information fade in/out on focus\nMusic continuity upon entering new maps\nStat window (F2)\nSkill particles effects used by all entities\nAdvanced overworld warping\nSettings improvement\nTips window\nItems, Emotes and skills shortcuts\n[/ul]\n\nCode:\n[ul]\nUpgrade to Godot 4.2.1\nDeath logic\nDefault AI for wandering entities\nExperience and level-up system\nAdvanced stat formulas\nAdvanced combat and skills\nNetwork support for item usage and inventory\nDevice/input manager\nClear network code and use network channels\nCallback and various utils static classes\nUnify config files within our editor\nCode clean up and refactoring\nLighting optimization for mobile and low-end devices\nAdvanced resources management\nDeferred instantiation\n[/ul]\n\nThis update focus on implementing reliable combat, skill and AI systems while improving our current content and art direction toward our targeted level of quality for SoM.\nThank you for all your feedbacks and comments throughout the development of this version!\n"}, {"title": "Version 0.0.5", "date": "2023-12-16", "content": "Gameplay Enhancements:\n[ul]Different player sprite and walking speed on the overworld map.\nMorph feature allowing dynamic player transformations.\nIntroduction of a setting menu for player customization.\nCustom UI for mobile devices.\nEnhanced monster interaction, featuring kill, spawn, and automatic respawn capabilities.\nImplementation of attack animations and action support.\nOngoing improvement of the game Finite State Machine.\nTile animation and cave effects for an immersive experience.\nReworked game UI enter/exit flow for a smoother user experience.\nDynamic position and animations to display damage, dialogue and emotes information.\nGender and player sprite color support.\nIntroduction of new emotes for richer player expression.[/ul]\n\nCode:\n[ul]Implementation of the stat system.\nCode organization with more node-oriented refacto.\nBlendTree support for our animations.\nOpenGL3 builds by default.\nNetwork replication optimizations.[/ul]\n\nProject:\n[ul]GitHub mirror for improved visibility.\nImplementation of an online list web page for player tracking.[/ul]\n\nThis update brings Source of Mana closer to its vision by setting our UI/UX to the standard we want to aim for the rest of the development of this project.\nYour feedback is invaluable as we shape the future of the game together!\n"}, {"title": "Version 0.0.4", "date": "2023-03-15", "content": "Game:\n[ul]Add a new zone, the skull cave map[/ul]\nCode:\n[ul]Splitting navigation export from the server export and make it optional\nRemove all function call used by the server that use game nodes else than specific server data, db and conf files\nSplit Entity code for a client/server use\nAdd basic client/server connection (ip:port connection)\nAdd basic entity sync with a client/server (pos, animation)\nAdd state sync with a client/server\nAdd emote sync with a client/server\nAdd chat sync with a client/server\nScreenshot feature\nNPC support split from mob support\nBasic NPC interaction\nChat history feature (Up/Down keys)[/ul]\nProduction:\n[ul]Generate two binaries through the same project, be able to run in hybrid and in client/server\nZoomed viewport for mobile and large screen.[/ul]\n"}, {"title": "Version 0.0.3", "date": "2023-01-26", "content": "Game:\n[ul]Add various new slime mobs\nComplete the slime cave design\nAdd an overworld map concept[/ul]\nCode:\n[ul]General UI improvement\nItem management\nChat support (but local only)\nMap import improvement\nEntity refactorisation\nNPC/Monster support\nFull navigation support\nDefault AI\nSplit most of our code to work in client/server[/ul]\nDesign:\n[ul]Game Design Document update\nWorld map concept\nQuest & Narrative design in progress[/ul]\nProject:\n[ul]Create a new Art & Design repository to store most of our assets[/ul]\n"}, {"title": "Version 0.0.2", "date": "2022-10-02", "content": "\tHello Manaverse!\nWe bring you the second step in this project, on the second day of the second month from publication!\nToday we're finally releasing the 0.0.2 version of Source of Mana. A lot of things are being built from scratch or are newly adapted for Godot, so most of these changes are designed to prepare the game for a future prototype release. New builds are now available for download. We encourage you to take a look and let us know what you think.\n\nSource of Mana has grown a lot over the past month, our seedling of a project is finally sprouting into the wonderful tree it will one day become. The 0.0.1 release was downloaded nearly 100 times, which we consider pretty good for such a small prototype.\n\nChangelog:\n[ul]Game ported to Godot 4.0 (Beta 2) after initially being built on Godot 3.5;\nAdded minimap support;\nImproved Tiled map imports to work with Godot 4.0;\nAdded various GUI elements including windows, buttons, and fonts;\nAdded support for emotes (as well as new TMW-style emotes that will be coming with future releases!);\nVarious GUI, navigation, and build fixes;\nNew story design in progress for a central storyline and to incorporate Mana into our World.[/ul]\n"}, {"title": "Version 0.0.1", "date": "2023-08-31", "content": "\tHello Mana world,\n\nOur prototype and first version of Source of Mana is now available to download on our itch.io page.\n\nFor this first version, the changelog would be pretty large, so instead, I prefer to present you on the main lines what we have been trying to achieve:\n\nOur ambition was to create a working offline prototype of what could be TMW within Godot in 2022, we prepared a basic code, asset, project architectures, and a game design document to support our game development.\n\nOur code rolls with a service system that can be enabled/disabled from a main scene. Multiple scenes will be created within the same Godot project to generate a server, a client, and a launcher.\n\nOur asset regroups what was previously done on TMW and rEvolt, we are preparing a new git repository to work like a giant asset store that features all available content that can be featured on SoM based on the quality and needs of these assets.\n\nOur project will be managed through Gitlab's built-in agile planning with a \"Milestone\" release plan.\n\nOur game design is currently being redacted into a Game Design Document that will feature all of the main designs and focus we will want to address.\n\nAside from all of these plans, our First Playable Preview release features a playable character with basic walk and sit actions that can wander inside a cave by mouse, keyboard, and gamepad.\nGodot's benefits are many but the most important is the easy release process that helps us to prepare builds for different platforms like Linux, MacOS, Windows, and Android.\n"}]}]}