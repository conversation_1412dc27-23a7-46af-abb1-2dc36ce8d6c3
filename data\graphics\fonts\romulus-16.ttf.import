[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://bxtecswl0imti"
path="res://.godot/imported/romulus-16.ttf-b317c5c3b1c22cdc48bb26d63e72d6d2.fontdata"

[deps]

source_file="res://data/graphics/fonts/romulus-16.ttf"
dest_files=["res://.godot/imported/romulus-16.ttf-b317c5c3b1c22cdc48bb26d63e72d6d2.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
modulate_color_glyphs=false
hinting=1
subpixel_positioning=1
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
