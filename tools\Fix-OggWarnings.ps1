# Script PowerShell para corrigir warnings de comentarios invalidos em arquivos OGG Vorbis
# Este script forca a reimportacao dos arquivos no Godot com configuracoes limpas

param(
    [string]$MusicPath = "data\music",
    [switch]$CreateBackup = $true,
    [switch]$Verbose = $false
)

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARNING" { "Yellow" }
        "SUCCESS" { "Green" }
        default { "White" }
    }
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

function Get-OggFiles {
    param([string]$Path)
    
    if (-not (Test-Path $Path)) {
        Write-Log "Pasta não encontrada: $Path" "ERROR"
        return @()
    }
    
    $oggFiles = Get-ChildItem -Path $Path -Filter "*.ogg" -File
    Write-Log "Encontrados $($oggFiles.Count) arquivos OGG em $Path"
    
    return $oggFiles
}

function Backup-File {
    param([System.IO.FileInfo]$File)
    
    if (-not $CreateBackup) {
        return $true
    }
    
    $backupPath = "$($File.FullName).backup"
    
    try {
        Copy-Item -Path $File.FullName -Destination $backupPath -Force
        Write-Log "Backup criado: $($File.Name).backup" "SUCCESS"
        return $true
    }
    catch {
        Write-Log "Erro ao criar backup para $($File.Name): $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Update-ImportFile {
    param([System.IO.FileInfo]$OggFile)
    
    $importPath = "$($OggFile.FullName).import"
    
    if (-not (Test-Path $importPath)) {
        Write-Log "Arquivo .import não encontrado: $($OggFile.Name).import" "WARNING"
        return $false
    }
    
    try {
        # Lê o conteúdo do arquivo .import
        $content = Get-Content -Path $importPath -Raw
        
        # Gera novo UID para forçar reimportação
        $newUid = "uid://b" + [System.Guid]::NewGuid().ToString("N").Substring(0, 12)
        
        # Substitui o UID existente
        $content = $content -replace 'uid="[^"]*"', "uid=`"$newUid`""
        
        # Gera novo hash para o arquivo de destino
        $newHash = [System.Guid]::NewGuid().ToString("N")
        $newDestFile = "res://.godot/imported/$($OggFile.Name)-$newHash.oggvorbisstr"
        
        # Atualiza o caminho de destino
        $content = $content -replace 'path="[^"]*"', "path=`"$newDestFile`""
        $content = $content -replace 'dest_files=\["[^"]*"\]', "dest_files=[`"$newDestFile`"]"
        
        # Salva o arquivo modificado
        Set-Content -Path $importPath -Value $content -Encoding UTF8
        
        Write-Log "Arquivo .import atualizado: $($OggFile.Name).import" "SUCCESS"
        return $true
    }
    catch {
        Write-Log "Erro ao atualizar .import para $($OggFile.Name): $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Test-FFmpeg {
    try {
        $null = Get-Command ffmpeg -ErrorAction Stop
        return $true
    }
    catch {
        return $false
    }
}

function Fix-OggWithFFmpeg {
    param([System.IO.FileInfo]$InputFile)
    
    $tempFile = "$($InputFile.DirectoryName)\temp_$($InputFile.Name)"
    
    try {
        # Comando ffmpeg para recodificar sem metadados problemáticos
        $ffmpegArgs = @(
            "-i", $InputFile.FullName,
            "-c:a", "libvorbis",
            "-q:a", "5",
            "-map_metadata", "-1",
            "-y",
            $tempFile
        )
        
        $process = Start-Process -FilePath "ffmpeg" -ArgumentList $ffmpegArgs -Wait -PassThru -NoNewWindow -RedirectStandardError "ffmpeg_error.log"
        
        if ($process.ExitCode -eq 0 -and (Test-Path $tempFile)) {
            # Substitui o arquivo original
            Move-Item -Path $tempFile -Destination $InputFile.FullName -Force
            Write-Log "Arquivo recodificado com ffmpeg: $($InputFile.Name)" "SUCCESS"
            return $true
        }
        else {
            Write-Log "FFmpeg falhou para $($InputFile.Name)" "ERROR"
            if (Test-Path $tempFile) {
                Remove-Item $tempFile -Force
            }
            return $false
        }
    }
    catch {
        Write-Log "Erro ao executar ffmpeg: $($_.Exception.Message)" "ERROR"
        if (Test-Path $tempFile) {
            Remove-Item $tempFile -Force
        }
        return $false
    }
}

function Main {
    Write-Log "=== Iniciando correcao de warnings OGG Vorbis ===" "SUCCESS"
    Write-Log "Pasta de musica: $MusicPath"

    # Verifica se a pasta existe
    if (-not (Test-Path $MusicPath)) {
        Write-Log "Pasta de musica nao encontrada: $MusicPath" "ERROR"
        return 1
    }

    # Encontra arquivos OGG
    $oggFiles = Get-OggFiles -Path $MusicPath

    if ($oggFiles.Count -eq 0) {
        Write-Log "Nenhum arquivo OGG encontrado" "WARNING"
        return 0
    }

    # Verifica se ffmpeg esta disponivel
    $hasFFmpeg = Test-FFmpeg
    if ($hasFFmpeg) {
        Write-Log "FFmpeg detectado - sera usado para recodificacao" "SUCCESS"
    }
    else {
        Write-Log "FFmpeg nao encontrado - apenas atualizando arquivos .import" "WARNING"
    }
    
    $successCount = 0
    $errorCount = 0
    
    # Processa cada arquivo
    foreach ($oggFile in $oggFiles) {
        Write-Log "Processando: $($oggFile.Name)"
        
        # Cria backup se solicitado
        if ($CreateBackup) {
            if (-not (Backup-File -File $oggFile)) {
                $errorCount++
                continue
            }
        }
        
        # Tenta recodificar com ffmpeg se disponivel
        if ($hasFFmpeg) {
            if (Fix-OggWithFFmpeg -InputFile $oggFile) {
                Write-Log "Recodificacao bem-sucedida: $($oggFile.Name)" "SUCCESS"
            }
            else {
                Write-Log "Falha na recodificacao: $($oggFile.Name)" "WARNING"
            }
        }

        # Atualiza arquivo .import
        if (Update-ImportFile -OggFile $oggFile) {
            $successCount++
        }
        else {
            $errorCount++
        }
    }

    Write-Log "=== Processamento concluido ===" "SUCCESS"
    Write-Log "Arquivos processados com sucesso: $successCount" "SUCCESS"
    Write-Log "Arquivos com erro: $errorCount" $(if ($errorCount -gt 0) { "ERROR" } else { "SUCCESS" })
    
    Write-Log ""
    Write-Log "PROXIMOS PASSOS:" "SUCCESS"
    Write-Log "1. Abra o projeto no Godot"
    Write-Log "2. Va em Project -> Reimport"
    Write-Log "3. Selecione todos os arquivos .ogg na pasta data/music"
    Write-Log "4. Clique em Reimport"
    Write-Log "5. Execute o projeto para verificar se os warnings sumiram"
    
    return $(if ($errorCount -gt 0) { 1 } else { 0 })
}

# Executa o script principal
exit (Main)
